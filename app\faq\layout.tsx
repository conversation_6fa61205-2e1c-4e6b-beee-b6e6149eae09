import { Metadata } from 'next';

export const metadata: Metadata = {
    title: 'Frequently Asked Questions | Detail On The Go',
    description: 'Find answers to common questions about Detail On The Go’s mobile detailing services, booking, pricing, and more.',
    keywords: [
        'FAQ',
        'frequently asked questions',
        'auto detailing FAQ',
        'mobile detailing questions',
        'Detail On The Go help',
        'car detailing info'
    ],
    alternates: {
        canonical: 'https://detailongo.com/faq/',
    },
    openGraph: {
        title: 'Frequently Asked Questions | Detail On The Go',
        description: 'Answers to common questions about our mobile detailing services, booking, and more.',
        url: 'https://detailongo.com/faq/',
        images: [
            {
                url: '/images/faq-og.jpg',
                width: 1200,
                height: 630,
                alt: 'FAQ - Detail On The Go'
            }
        ],
        type: 'website',
    },
    twitter: {
        card: 'summary_large_image',
        title: 'Frequently Asked Questions | Detail On The Go',
        description: 'Answers to common questions about our mobile detailing services, booking, and more.',
        images: ['/images/faq-og.jpg'],
    }
};

export default function Layout({ children }: { children: React.ReactNode }) {
    return (
        <>
            {/* Structured Data for SEO */}
            <script
                type="application/ld+json"
                dangerouslySetInnerHTML={{
                    __html: JSON.stringify({
                        "@context": "https://schema.org",
                        "@type": "FAQPage",
                        "mainEntity": [
                            // Add your FAQ questions/answers here for enhanced SEO
                            // Example:
                            // {
                            //   "@type": "Question",
                            //   "name": "What services do you offer?",
                            //   "acceptedAnswer": {
                            //     "@type": "Answer",
                            //     "text": "We offer mobile car, boat, and RV detailing, ceramic coating, and more."
                            //   }
                            // }
                        ],
                        "url": "https://detailongo.com/faq/",
                        "publisher": {
                            "@type": "Organization",
                            "name": "Detail On The Go",
                            "url": "https://detailongo.com/",
                            "image": "https://detailongo.com/images/faq-og.jpg",
                            "telephone": "******-615-6156"
                        }
                    })
                }}
            />
            {children}
        </>
    );
}
