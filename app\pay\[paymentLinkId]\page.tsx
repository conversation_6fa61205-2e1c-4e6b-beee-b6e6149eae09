'use client';

import { doc, getDoc, updateDoc } from 'firebase/firestore';
import { db } from '@/lib/firebase/firebase';
import PaymentForm from '@/components/PaymentForm';
import { useEffect, useState, useCallback } from 'react';
import { calculateTax, TaxCalculationResult } from '@/lib/taxCalculator';
import AddressInput2 from '@/components/AddressInput2';

interface TaxInfo {
    totalBeforeTax: number;
    taxRate: number;
    totalTax: number;
    totalAfterTax: number;
    taxRateDisplay?: string; // Store original formatted rate
    error?: string;
}

interface AddressData {
    address: string;
    street: string;
    city: string;
    state: string;
    postalCode: string;
}

export default function PaymentPage({ params }: { params: { paymentLinkId: string } }) {
    const [paymentLink, setPaymentLink] = useState<any>(null);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState('');
    const [emailSending, setEmailSending] = useState(false);
    const [emailSuccess, setEmailSuccess] = useState(false);

    // Tax calculation state
    const [address, setAddress] = useState<AddressData>({
        address: '',
        street: '',
        city: '',
        state: '',
        postalCode: ''
    });

    const [taxInfo, setTaxInfo] = useState<TaxInfo>({
        totalBeforeTax: 0,
        taxRate: 0,
        totalTax: 0,
        totalAfterTax: 0,
    });

    // Calculate tax when address changes
    useEffect(() => {
        const calculateTaxForPayment = async () => {
            const subtotal = paymentLink?.amount || 0;

            // Check if current address matches original booking address
            const originalAddress = paymentLink?.bookingDetails?.stripeAddress;
            const addressChanged = originalAddress && (
                address.street !== originalAddress.line1 ||
                address.city !== originalAddress.city ||
                address.state !== originalAddress.state ||
                address.postalCode !== originalAddress.postal_code
            );

            // Check if we have a complete address
            const hasCompleteAddress = address.street && address.city && address.state && address.postalCode;

            console.log('Tax calculation check:', {
                street: address.street,
                city: address.city,
                state: address.state,
                postalCode: address.postalCode,
                subtotal,
                addressChanged,
                hasOriginalTax: !!paymentLink?.originalTax,
                hasCompleteAddress,
                originalAddress: originalAddress ? {
                    line1: originalAddress.line1,
                    city: originalAddress.city,
                    state: originalAddress.state,
                    postal_code: originalAddress.postal_code
                } : null
            });

            // Calculate tax if:
            // 1. We have a complete address AND
            // 2. (Address has changed from original OR no original tax info exists)
            // Note: We always recalculate if no original tax exists to ensure tax is calculated
            const shouldCalculateTax = hasCompleteAddress && (addressChanged || !paymentLink?.originalTax);

            if (shouldCalculateTax) {
                console.log('Running tax calculation due to address change or missing original tax...');
                try {
                    const taxData = {
                        addressStreet: address.street,
                        addressCity: address.city,
                        addressState: address.state,
                        addressPostal: address.postalCode,
                        addressCountry: "US",
                        amount: subtotal,
                    };

                    const taxResult = await calculateTax(taxData);
                    console.log('Tax result:', taxResult);

                    if (!taxResult.error) {
                        const taxRate = parseFloat(taxResult.taxRate);
                        const taxAmount = parseFloat(taxResult.totalTax);
                        const totalAmount = subtotal + taxAmount;

                        setTaxInfo({
                            totalBeforeTax: subtotal,
                            taxRate: taxRate,
                            totalTax: taxAmount,
                            totalAfterTax: totalAmount,
                            taxRateDisplay: taxResult.taxRate,
                        });
                    } else {
                        console.error('Tax calculation returned error:', taxResult.error);
                        // Set fallback tax info with error
                        setTaxInfo({
                            totalBeforeTax: subtotal,
                            taxRate: 0,
                            totalTax: 0,
                            totalAfterTax: subtotal,
                            error: taxResult.error,
                        });
                    }
                } catch (error) {
                    console.error("Tax calculation failed:", error);
                    // Set fallback tax info
                    setTaxInfo({
                        totalBeforeTax: subtotal,
                        taxRate: 0,
                        totalTax: 0,
                        totalAfterTax: subtotal,
                        error: "Failed to calculate tax",
                    });
                }
            } else if (!hasCompleteAddress) {
                console.log('Address incomplete, setting zero tax');
                setTaxInfo({
                    totalBeforeTax: subtotal,
                    taxRate: 0,
                    totalTax: 0,
                    totalAfterTax: subtotal,
                });
            } else {
                console.log('Using original tax info - address unchanged');
                // Address hasn't changed, keep using original tax info (already set in fetchPaymentLink)
            }
        };

        // Run calculation if we have paymentLink data
        if (paymentLink) {
            calculateTaxForPayment();
        }
    }, [address.street, address.city, address.state, address.postalCode, paymentLink?.amount, paymentLink?.originalTax, paymentLink?.bookingDetails?.stripeAddress]);

    const fetchPaymentLink = useCallback(async () => {
        try {
            const docRef = doc(db, 'paymentLinks', params.paymentLinkId);
            const docSnap = await getDoc(docRef);

            if (!docSnap.exists()) {
                setError('We couldn\'t find this payment link, <NAME_EMAIL>.');
                return;
            }

            const data = docSnap.data();
            setPaymentLink(data);

            // Auto-fill address from booking data
            if (data.bookingDetails?.stripeAddress) {
                const stripeAddr = data.bookingDetails.stripeAddress;
                setAddress({
                    address: data.bookingDetails.address || '',
                    street: stripeAddr.line1 || '',
                    city: stripeAddr.city || '',
                    state: stripeAddr.state || '',
                    postalCode: stripeAddr.postal_code || ''
                });
            }

            // Use original tax information from booking if available
            if (data.originalTax) {
                setTaxInfo({
                    totalBeforeTax: data.amount || 0,
                    taxRate: data.originalTax.taxRate || 0,
                    totalTax: data.originalTax.taxAmount || 0,
                    totalAfterTax: data.originalTax.totalWithTax || data.amount || 0,
                    taxRateDisplay: data.originalTax.taxRateDisplay || '0.00',
                });
            } else {
                // Fallback to original behavior if no tax info stored
                setTaxInfo({
                    totalBeforeTax: data.amount || 0,
                    taxRate: 0,
                    totalTax: 0,
                    totalAfterTax: data.amount || 0,
                });
            }

            // Update the document to indicate it's been viewed
            await updateDoc(docRef, {
                viewed: true,
                lastViewed: new Date().toISOString(),
                viewCount: (data.viewCount || 0) + 1,
                isOpened: true
            });

        } catch (err) {
            console.error('Error fetching payment link:', err);
            setError('Something went wrong. Please try again.');
        } finally {
            setLoading(false);
        }
    }, [params.paymentLinkId]);

    useEffect(() => {
        fetchPaymentLink();

        // Listen for payment success event from PaymentForm
        const handlePaymentSuccessEvent = (event: any) => {
            handlePaymentSuccess(event.detail);
        };

        window.addEventListener('paymentSuccess', handlePaymentSuccessEvent);

        return () => {
            window.removeEventListener('paymentSuccess', handlePaymentSuccessEvent);
        };
    }, [fetchPaymentLink]);

    const handlePaymentSuccess = (paymentData: any) => {
        // Ensure we have the most complete data by re-fetching from Firebase
        const fetchUpdatedData = async () => {
            try {
                // Add a small delay to ensure Firebase update is complete
                await new Promise(resolve => setTimeout(resolve, 800));

                const docRef = doc(db, 'paymentLinks', params.paymentLinkId);
                const docSnap = await getDoc(docRef);

                if (docSnap.exists()) {
                    const freshData = docSnap.data();

                    // Create the complete updated payment link object
                    const updatedPaymentLink = {
                        ...paymentLink, // Original data
                        ...freshData, // Fresh data from Firebase
                        // Ensure critical fields are set
                        isPaid: true,
                        paidAt: freshData.paidAt || new Date().toISOString(),
                        paymentMethod: freshData.paymentMethod || paymentData.paymentMethod,
                        customerName: freshData.customerName || paymentData.customerName,
                        customerEmail: freshData.customerEmail || paymentData.customerEmail,
                        customerPhone: freshData.customerPhone || paymentData.customerPhone,
                        customerAddress: freshData.customerAddress || paymentData.customerAddress,
                        wantsToReview: freshData.wantsToReview !== undefined ? freshData.wantsToReview : paymentData.wantsToReview,
                        reviewLink: freshData.reviewLink || paymentData.reviewLink || paymentLink?.reviewLink || 'https://g.page/r/CS98X9jMS0IREBM/review'
                    };

                    // Debug log to check if all data is present
                    console.log('Updated payment link with fresh data:', updatedPaymentLink);

                    // Update the state with this new object to trigger the re-render
                    setPaymentLink(updatedPaymentLink);

                    // Send combined email with the complete data
                    setTimeout(() => {
                        sendAndAskForReviewEmail(
                            updatedPaymentLink.customerEmail,
                            updatedPaymentLink.customerName,
                            updatedPaymentLink
                        );
                    }, 1000);

                    // If customer opted-in to review, open the review link with the correct URL
                    if (updatedPaymentLink.wantsToReview && updatedPaymentLink.reviewLink) {
                        setTimeout(() => {
                            window.open(updatedPaymentLink.reviewLink, '_blank');
                        }, 2000);
                    }
                } else {
                    // Fallback to original method if Firebase fetch fails
                    const updatedPaymentLink = {
                        ...paymentLink,
                        isPaid: true,
                        paidAt: new Date().toISOString(),
                        paymentMethod: paymentData.paymentMethod,
                        customerName: paymentData.customerName,
                        customerEmail: paymentData.customerEmail,
                        customerPhone: paymentData.customerPhone,
                        wantsToReview: paymentData.wantsToReview,
                        reviewLink: paymentData.reviewLink || paymentLink?.reviewLink || 'https://g.page/r/CS98X9jMS0IREBM/review'
                    };

                    setPaymentLink(updatedPaymentLink);
                }
            } catch (error) {
                console.error('Error fetching updated payment data:', error);

                // Fallback to original method if error occurs
                const updatedPaymentLink = {
                    ...paymentLink,
                    isPaid: true,
                    paidAt: new Date().toISOString(),
                    paymentMethod: paymentData.paymentMethod,
                    customerName: paymentData.customerName,
                    customerEmail: paymentData.customerEmail,
                    customerPhone: paymentData.customerPhone,
                    wantsToReview: paymentData.wantsToReview,
                    reviewLink: paymentData.reviewLink || paymentLink?.reviewLink || 'https://g.page/r/CS98X9jMS0IREBM/review'
                };

                setPaymentLink(updatedPaymentLink);

                // Still send email with available data
                setTimeout(() => {
                    sendAndAskForReviewEmail(
                        updatedPaymentLink.customerEmail,
                        updatedPaymentLink.customerName,
                        updatedPaymentLink
                    );
                }, 1000);
            }
        };

        // Execute the fetch
        fetchUpdatedData();
    };

    // Safe render for amount
    const renderAmount = (amount: number | undefined) => {
        return amount ? amount.toFixed(2) : '0.00';
    };

    // Modified to accept the payment link data directly and use the correct review link
    const sendAndAskForReviewEmail = async (customerEmail: string, customerName: string, linkData: any) => {
        try {
            const receiptHTML = generateReceiptHTML(linkData);

            // Use the actual review link from the data, with fallback
            const reviewLinkToUse = linkData.reviewLink || 'https://g.page/r/CS98X9jMS0IREBM/review';

            const combinedMessageHTML = `
                <div style="font-family: Helvetica, Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; border: 1px solid #e5e7eb; border-radius: 8px;">
                    <h2 style="color: #1e40af; font-family: Helvetica, Arial, sans-serif;">Hi ${customerName?.split(' ')[0] || 'there'},</h2>
                    <p style="font-family: Helvetica, Arial, sans-serif;">Thank you for your business! Your feedback is incredibly valuable to us. It helps us improve our services and support our team.</p>
                    <div style="background: #f8fafc; padding: 30px; border-radius: 8px; margin: 30px 0; text-align: center;">
                        <h3 style="color: #374151; margin-bottom: 20px; font-family: Helvetica, Arial, sans-serif;">Would you mind leaving us a Google review?</h3>
                        <a href="${reviewLinkToUse}" 
                           style="display: inline-block; background: #1e40af; color: white; padding: 15px 30px; text-decoration: none; border-radius: 8px; font-weight: bold; font-size: 16px; margin: 10px 0; font-family: Helvetica, Arial, sans-serif;">
                            Leave a Google Review ⭐
                        </a>
                        <p style="margin-top: 20px; font-size: 14px; color: #6b7280; font-family: Helvetica, Arial, sans-serif;">
                            It only takes a minute and means the world to us!
                        </p>
                    </div>
                    <p style="color: #6b7280; margin-top: 30px; font-family: Helvetica, Arial, sans-serif;">
                        Best,<br>
                        Detail On The Go
                    </p>
                    <div style="border-top: 2px dashed #ccc; margin-top: 30px; padding-top: 30px;">
                        ${receiptHTML}
                    </div>
                </div>
            `;

            const payload = {
                fromName: "Detail On The Go",
                fromEmail: "<EMAIL>",
                to: customerEmail,
                subject: `Your Receipt & Feedback for ${linkData.productName || 'Service'}`,
                message: combinedMessageHTML
            };

            await fetch('/api/send-email', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify(payload)
            });

        } catch (error) {
            console.error('Error sending combined email:', error);
        }
    };

    // Modified to accept linkData as an argument instead of using state
    const generateReceiptHTML = (linkData: any) => {
        const amount = linkData.amount ? linkData.amount.toFixed(2) : '0.00';
        const paidAt = linkData.paidAt ? new Date(linkData.paidAt) : new Date();

        return `
            <!DOCTYPE html>
            <html>
            <head>
                <meta charset="utf-8">
                <title>Receipt - Detail On The Go</title>
                <style>
                    body { font-family: Helvetica, 'Courier New', monospace; max-width: 400px; margin: 0 auto; padding: 20px; }
                    .receipt { border: 2px dashed #000; padding: 30px; background: white; }
                    .header { text-align: center; border-bottom: 2px dashed #000; padding-bottom: 15px; margin-bottom: 20px; }
                    .business-name { font-size: 16px; font-weight: bold; margin-bottom: 5px; font-family: Helvetica, Arial, sans-serif; }
                    .receipt-title { font-size: 18px; font-weight: bold; margin-bottom: 10px; font-family: Helvetica, Arial, sans-serif; }
                    .branch, .date { font-size: 12px; margin-bottom: 5px; font-family: Helvetica, Arial, sans-serif; }
                    .item { display: flex; justify-content: space-between; margin-bottom: 10px; }
                    .description { font-size: 12px; color: #666; margin-top: 5px; font-family: Helvetica, Arial, sans-serif; }
                    .total-section { border-top: 2px dashed #000; padding-top: 15px; margin-top: 20px; margin-bottom: 20px; }
                    .total { display: flex; justify-content: space-between; font-weight: bold; font-size: 16px; margin-bottom: 10px; font-family: Helvetica, Arial, sans-serif; }
                    .payment-method { font-size: 12px; display: flex; justify-content: space-between; font-family: Helvetica, Arial, sans-serif; }
                    .customer-section { border-top: 2px dashed #000; padding-top: 15px; margin-top: 20px; margin-bottom: 20px; font-size: 12px; }
                    .customer-copy { font-weight: bold; margin-bottom: 10px; font-family: Helvetica, Arial, sans-serif; }
                    .customer-info { margin-bottom: 5px; font-family: Helvetica, Arial, sans-serif; }
                    .footer { border-top: 2px dashed #000; padding-top: 15px; text-align: center; font-size: 12px; }
                    .footer div { margin-bottom: 5px; font-family: Helvetica, Arial, sans-serif; }
                </style>
            </head>
            <body>
                <div class="receipt">
                    <div class="header">
                        <div class="business-name">DETAIL ON THE GO, LLC</div>
                        <div class="receipt-title">RECEIPT</div>
                        <div class="branch">${linkData.branch?.toUpperCase() || "UDF"}</div>
                        <div class="date">${paidAt.toLocaleDateString('en-US', {
            year: 'numeric',
            month: '2-digit',
            day: '2-digit',
            hour: '2-digit',
            minute: '2-digit'
        })}</div>
                    </div>
                    
                    <div class="item">
                        <div>
                            <div><strong>${linkData.productName || 'Service'}</strong></div>
                            ${linkData.description ? `<div class="description">${linkData.description}</div>` : ''}
                        </div>
                        <div><strong>$${linkData.taxInfo?.originalAmount?.toFixed(2) || amount}</strong></div>
                    </div>
                    
                    ${linkData.taxInfo?.taxAmount > 0 ? `
                    <div class="item">
                        <div><strong>Sales Tax</strong></div>
                        <div><strong>$${linkData.taxInfo.taxAmount.toFixed(2)}</strong></div>
                    </div>
                    ` : ''}
                    
                    ${linkData.taxInfo?.tipAmount > 0 ? `
                    <div class="item">
                        <div><strong>Tip</strong></div>
                        <div><strong>$${linkData.taxInfo.tipAmount.toFixed(2)}</strong></div>
                    </div>
                    ` : ''}
                    
                    <div class="total-section">
                        <div class="total">
                            <span>TOTAL</span>
                            <span>$${linkData.taxInfo?.totalAmount?.toFixed(2) || amount}</span>
                        </div>
                        ${linkData.paymentMethod?.last4 ? `
                        <div class="payment-method">
                            <span>Card ending in ${linkData.paymentMethod.last4}</span>
                            <span>APPROVED</span>
                        </div>
                        ` : ''}
                    </div>
                    
                    <div class="customer-section">
                        <div class="customer-copy">CUSTOMER COPY</div>
                        <div class="customer-info">${linkData.customerName || ''}</div>
                        <div class="customer-info">${linkData.customerEmail?.replace(/(.{3}).*@/, "$1***@") || ''}</div>
                        ${linkData.customerPhone ? `<div class="customer-info">*** *** ${linkData.customerPhone.slice(-4)}</div>` : ''}
                        ${linkData.customerAddress ? `<div class="customer-info">${linkData.customerAddress}</div>` : ''}
                    </div>
                    
                    <div class="footer">
                        <div>Payment ID: ${params.paymentLinkId.slice(-8)}</div>
                        <div><strong>✓ Payment confirmed</strong></div>
                        <div>Thank you for your payment!</div>
                    </div>
                </div>
            </body>
            </html>
        `;
    };

    const downloadReceiptPDF = () => {
        const receiptHTML = generateReceiptHTML(paymentLink);
        const blob = new Blob([receiptHTML], { type: 'text/html' });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `receipt-${params.paymentLinkId.slice(-8)}.html`;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);
    };

    const emailReceipt = async () => {
        if (!paymentLink.customerEmail) {
            alert('No email address found for this receipt.');
            return;
        }

        setEmailSending(true);
        try {
            await sendAndAskForReviewEmail(paymentLink.customerEmail, paymentLink.customerName, paymentLink);
            setEmailSuccess(true);
            setTimeout(() => setEmailSuccess(false), 3000);
        } catch (err) {
            console.error("Email Error:", err);
            alert('Failed to send email. Please try again.');
        } finally {
            setEmailSending(false);
        }
    };

    const openReviewLink = () => {
        // Use the actual review link from payment data, with fallback
        const reviewLinkToUse = paymentLink.reviewLink || 'https://g.page/r/CS98X9jMS0IREBM/review';

        // Add 2 second delay before opening review link
        setTimeout(() => {
            window.open(reviewLinkToUse, '_blank');
        }, 2000);
    };

    // --- New Test Button Handler ---
    const handleTestEmail = () => {
        if (paymentLink) {
            // Create a mock data object to ensure all fields for the email are present
            const testLinkData = {
                ...paymentLink,
                isPaid: true,
                paidAt: paymentLink.paidAt || new Date().toISOString(),
                customerEmail: paymentLink.customerEmail || '<EMAIL>',
                customerName: paymentLink.customerName || 'Test Customer',
                paymentMethod: paymentLink.paymentMethod || { last4: 'TEST' },
                reviewLink: paymentLink.reviewLink || 'https://g.page/r/CS98X9jMS0IREBM/review'
            };

            sendAndAskForReviewEmail(
                testLinkData.customerEmail,
                testLinkData.customerName,
                testLinkData
            );
            alert(`Test email sent to ${testLinkData.customerEmail}`);
        } else {
            alert('Payment link data not loaded yet. Cannot send test email.');
        }
    };

    if (loading) {
        return (
            <div className="min-h-screen flex flex-col items-center justify-center text-white font-helvetica">
                <div className="flex justify-center mb-6">
                    <div className="animate-spin rounded-full h-16 w-16 border-4 border-white border-t-transparent"></div>
                </div>
                <p className="text-2xl font-medium font-helvetica">Loading your checkout...</p>
            </div>
        );
    }

    if (error) {
        return <div className="text-red-500 text-center p-8 font-helvetica">{error}</div>;
    }

    return (
        <div className="p-6 max-w-lg mx-auto min-h-screen flex flex-col justify-center relative font-helvetica">

            {paymentLink && (
                <>
                    {paymentLink.isPaid ? (
                        <div className="bg-white border-2 border-dashed border-black p-8 rounded-none shadow-lg max-w-sm w-full mx-auto font-mono text-sm text-black">
                            {/* Receipt Header */}
                            <div className="text-center mb-6 border-b border-dashed border-black pb-4">
                                <div className="text-lg font-bold mb-1 text-black font-helvetica">DETAIL ON THE GO, LLC</div>
                                <div className="text-xl font-bold mb-2 text-black font-helvetica">RECEIPT</div>
                                <div className="text-xs text-black font-medium font-helvetica">
                                    Branch: {paymentLink?.branch?.toUpperCase() || "UDF"} {/* Fixed with fallback */}
                                </div>
                                <div className="text-xs text-black font-helvetica">
                                    {new Date(paymentLink.paidAt).toLocaleDateString('en-US', {
                                        year: 'numeric',
                                        month: '2-digit',
                                        day: '2-digit',
                                        hour: '2-digit',
                                        minute: '2-digit'
                                    })}
                                </div>
                            </div>

                            {/* Item Details */}
                            <div className="space-y-2 mb-6">
                                <div className="flex justify-between items-center">
                                    <span className="text-black font-helvetica">{paymentLink.productName || 'Service'}</span>
                                    <span className="text-black font-helvetica">{(paymentLink.taxInfo?.originalAmount || paymentLink.amount).toFixed(2)}</span>
                                </div>
                                
                                {paymentLink.taxInfo?.taxAmount > 0 && (
                                    <div className="flex justify-between items-center">
                                        <span className="text-black font-helvetica">Sales Tax</span>
                                        <span className="text-black font-helvetica">{paymentLink.taxInfo.taxAmount.toFixed(2)}</span>
                                    </div>
                                )}
                                
                                {paymentLink.taxInfo?.tipAmount > 0 && (
                                    <div className="flex justify-between items-center">
                                        <span className="text-black font-helvetica">Tip</span>
                                        <span className="text-black font-helvetica">{paymentLink.taxInfo.tipAmount.toFixed(2)}</span>
                                    </div>
                                )}
                            </div>

                            {/* Total Section */}
                            <div className="border-t border-dashed border-black pt-4 mb-6">
                                <div className="flex justify-between items-center mb-2">
                                    <span className="font-medium text-black font-helvetica">TOTAL</span>
                                    <span className="font-bold text-lg text-black font-helvetica">
                                        ${(paymentLink.taxInfo?.totalAmount || paymentLink.amount).toFixed(2)}
                                    </span>
                                </div>
                                {paymentLink.paymentMethod?.last4 && (
                                    <div className="flex justify-between items-center text-xs text-black">
                                        <span className="font-helvetica">Card ending in {paymentLink.paymentMethod.last4}</span>
                                        <span className="font-helvetica">APPROVED</span>
                                    </div>
                                )}
                            </div>

                            {/* Customer Info */}
                            <div className="border-t border-dashed border-black pt-4 mb-6 text-xs">
                                <div className="text-black mb-2 font-medium font-helvetica">CUSTOMER COPY</div>
                                <div className="space-y-1">
                                    <div className="text-black font-helvetica">{paymentLink.customerName}</div>
                                    <div className="text-black font-helvetica">
                                        {paymentLink.customerEmail?.replace(/(.{3}).*@/, "$1***@")}
                                    </div>
                                    {paymentLink.customerPhone && (
                                        <div className="text-black font-helvetica">
                                            *** *** {paymentLink.customerPhone.slice(-4)}
                                        </div>
                                    )}
                                    {paymentLink.customerAddress && (
                                        <div className="text-black font-helvetica">
                                            {paymentLink.customerAddress}
                                        </div>
                                    )}
                                </div>
                            </div>

                            {/* Footer */}
                            <div className="border-t border-dashed border-black pt-4 text-center text-xs text-black">
                                <div className="mb-2 font-helvetica">Payment ID: {params.paymentLinkId.slice(-8)}</div>
                                <div className="mb-2 font-medium font-helvetica">✓ Payment confirmed</div>
                                <div className="mb-4 font-helvetica">Thank you for your payment!</div>

                                {/* Action Buttons */}
                                <div className="space-y-3 pt-4 border-t border-dashed border-black">
                                    {paymentLink.wantsToReview === false && (
                                        <button
                                            onClick={emailReceipt}
                                            disabled={emailSending}
                                            className="w-full bg-blue-600 text-white py-2 px-4 rounded font-medium text-sm hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed font-helvetica"
                                        >
                                            {emailSending ? 'Sending...' : emailSuccess ? '✓ Email Sent!' : 'Email Receipt'}
                                        </button>
                                    )}

                                    {paymentLink.wantsToReview === true && (
                                        <div className="bg-green-50 p-4 rounded-lg">
                                            <p className="text-green-800 text-sm font-helvetica">
                                                ✓ A receipt has been emailed to you!<br />
                                            </p>
                                        </div>
                                    )}

                                    <button
                                        onClick={openReviewLink}
                                        className="w-full bg-green-600 text-white py-2 px-4 rounded font-medium text-sm hover:bg-green-700 font-helvetica"
                                    >
                                        Leave a Review ⭐
                                    </button>

                                    <button
                                        onClick={downloadReceiptPDF}
                                        className="w-full bg-gray-600 text-white py-2 px-4 rounded font-medium text-sm hover:bg-gray-700 font-helvetica"
                                    >
                                        Download Receipt
                                    </button>
                                </div>
                            </div>
                        </div>
                    ) : (
                        <div className="bg-white rounded-2xl shadow-xl border border-gray-100 overflow-hidden">
                            {/* Header Section */}
                            <div className="bg-blue-600 px-8 py-6 text-white">
                                <h1 className="text-2xl font-bold text-center mb-2 font-helvetica">
                                    Complete your payment
                                </h1>
                                <p className="text-blue-100 text-center text-sm mb-1 font-helvetica">
                                    Payment due upon service completion
                                </p>
                            </div>

                            {/* Product Details */}
                            <div className="px-8 py-6 border-b border-gray-100">
                                <h2 className="text-xl font-semibold text-gray-900 mb-1 font-helvetica">
                                    {paymentLink.productName}
                                </h2>
                                <p className="text-gray-600 text-sm leading-relaxed font-helvetica">
                                    {paymentLink.description}
                                </p>
                            </div>

                            {/* Tax Information Display */}
                            <div className="px-8 py-6 border-b border-gray-100">
                                <div className="space-y-2">
                                    {taxInfo.error ? (
                                        <div className="text-red-600">
                                            <p className="font-helvetica">⚠️ Tax calculation unavailable - verify your address</p>
                                            <p className="text-blue-900 font-helvetica">
                                                <span className="font-medium">Services Total:</span> $
                                                {taxInfo.totalBeforeTax.toFixed(2)}
                                            </p>
                                        </div>
                                    ) : (
                                        <>
                                            <p className="text-blue-900 font-helvetica">
                                                <span className="font-medium">Services Total:</span> $
                                                {taxInfo.totalBeforeTax.toFixed(2)}
                                            </p>
                                            {taxInfo.taxRate > 0 ? (
                                                <>
                                                    <p className="text-blue-900 font-helvetica">
                                                        <span className="font-medium">Sales Tax ({(taxInfo.taxRate).toFixed(2)}%):</span> $
                                                        {taxInfo.totalTax.toFixed(2)}
                                                    </p>
                                                    <p className="text-blue-900 font-semibold font-helvetica">
                                                        <span className="font-medium">Total:</span> $
                                                        {taxInfo.totalAfterTax.toFixed(2)}
                                                    </p>
                                                </>
                                            ) : (
                                                <>
                                                    <p className="text-green-600 font-helvetica">✅ No sales tax applies to this location</p>
                                                    <p className="text-blue-900 font-semibold font-helvetica">
                                                        <span className="font-medium">Total:</span> $
                                                        {taxInfo.totalBeforeTax.toFixed(2)}
                                                    </p>
                                                </>
                                            )}
                                        </>
                                    )}
                                </div>

                                {/* Debug Information (remove in production) */}
                                {process.env.NODE_ENV === 'development' && (
                                    <div className="mt-4 p-3 bg-gray-100 rounded text-xs text-gray-600">
                                        <p><strong>Debug Info:</strong></p>
                                        <p>Address: {address.street}, {address.city}, {address.state} {address.postalCode}</p>
                                        <p>Complete Address: {!!(address.street && address.city && address.state && address.postalCode) ? 'Yes' : 'No'}</p>
                                        <p>Tax Rate: {taxInfo.taxRate}%</p>
                                        <p>Tax Amount: ${taxInfo.totalTax.toFixed(2)}</p>
                                        <p>Error: {taxInfo.error || 'None'}</p>
                                    </div>
                                )}
                            </div>

                            {/* Payment Form */}
                            <div className="px-8 py-6">
                                <PaymentForm
                                    paymentLinkId={params.paymentLinkId}
                                    amount={taxInfo.totalAfterTax}
                                    originalAmount={paymentLink.amount}
                                    taxAmount={taxInfo.totalTax}
                                    taxRate={taxInfo.taxRateDisplay}
                                    branch={paymentLink.branch}
                                    description={paymentLink.description}
                                    productName={paymentLink.productName}
                                    disabled={!address.street || !address.city || !address.state || !address.postalCode}
                                    initialAddress={address}
                                    onTipChange={(tipAmount) => {
                                        // Update tax info to include tip in total
                                        setTaxInfo(prev => ({
                                            ...prev,
                                            totalAfterTax: prev.totalBeforeTax + prev.totalTax + tipAmount
                                        }));
                                    }}
                                    onAddressChange={(newAddress) => {
                                        console.log('Payment page: Address changed to:', newAddress);
                                        setAddress(newAddress);
                                        // Force immediate tax calculation for new address
                                        if (newAddress.street && newAddress.city && newAddress.state && newAddress.postalCode) {
                                            console.log('Payment page: Triggering immediate tax calculation');
                                        }
                                    }}
                                />
                            </div>

                            {/* Security Footer */}
                            <div className="px-8 py-4 bg-gray-50 border-t border-gray-100">
                                <div className="flex items-center justify-center text-xs text-gray-500">
                                    <svg className="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                                        <path fillRule="evenodd" d="M5 9V7a5 5 0 0110 0v2a2 2 0 012 2v5a2 2 0 01-2 2H5a2 2 0 01-2-2v-5a2 2 0 012-2zm8-2v2H7V7a3 3 0 016 0z" clipRule="evenodd" />
                                    </svg>
                                    <span className="font-helvetica">Your payment information is secure and encrypted</span>
                                </div>
                            </div>
                        </div>
                    )}
                </>
            )}
        </div>
    );
}
