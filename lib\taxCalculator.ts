// lib/taxCalculator.ts

// Define the types for your tax calculation
export interface TaxInput {
    addressStreet: string;
    addressCity: string;
    addressState: string;
    addressPostal: string;
    addressCountry: string;
    amount: number;
  }
  
  export interface TaxCalculationResult {
    totalBeforeTax: string;
    totalTax: string;
    totalAfterTax: string;
    taxRate: string;
    error?: string;
  }
  
  export async function calculateTax(taxData: TaxInput): Promise<TaxCalculationResult> {
    console.log('calculateTax called with:', taxData);

    try {
      // Validate input data
      if (!taxData.addressStreet || !taxData.addressCity || !taxData.addressState || !taxData.addressPostal) {
        throw new Error('Incomplete address data provided');
      }

      if (!taxData.amount || taxData.amount <= 0) {
        throw new Error('Invalid amount provided');
      }

      // Format the address for Stripe API
      const address = {
        line1: taxData.addressStreet,
        city: taxData.addressCity,
        state: taxData.addressState,
        postal_code: taxData.addressPostal,
        country: taxData.addressCountry,
      };

      console.log('Sending tax calculation request:', {
        amount: Math.round(taxData.amount * 100),
        address: address
      });

      // Call your server-side API endpoint
      const response = await fetch('/api/tax-calculation', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          amount: Math.round(taxData.amount * 100), // Convert to cents
          address: address,
        }),
      });

      console.log('Tax calculation response status:', response.status);

      if (!response.ok) {
        const errorText = await response.text();
        console.error('Tax calculation API error:', errorText);
        throw new Error(`Tax calculation failed: ${response.status} ${response.statusText} - ${errorText}`);
      }

      const data = await response.json();
      console.log('Tax calculation response data:', data);

      if (!data.success) {
        throw new Error(data.error || 'Unknown tax calculation error');
      }

      // Format the response to match what BookingSection expects
      const taxAmount = data.taxAmount / 100; // Convert cents to dollars
      // data.taxPercentage is already the correct percentage as a string
      const taxRate = data.taxPercentage.toString();

      const result = {
        totalBeforeTax: taxData.amount.toFixed(2),
        totalTax: taxAmount.toFixed(2),
        totalAfterTax: (taxData.amount + taxAmount).toFixed(2),
        taxRate: taxRate
      };

      console.log('Tax calculation result:', result);
      return result;
    } catch (error) {
      console.error('Tax calculation error:', error);

      // Return a fallback with no tax if calculation fails
      const fallbackResult = {
        totalBeforeTax: taxData.amount.toFixed(2),
        totalTax: '0.00',
        totalAfterTax: taxData.amount.toFixed(2),
        taxRate: '0.00',
        error: error instanceof Error ? error.message : 'Unknown error'
      };

      console.log('Tax calculation fallback result:', fallbackResult);
      return fallbackResult;
    }
  }
