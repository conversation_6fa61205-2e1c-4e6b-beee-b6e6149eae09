// app/layout.tsx
import "./globals.css";
import cx from "classnames";
import { helvetica } from "./fonts";
import Navbar from "@/components/layout/navbar";
import Footer from "@/components/layout/footer";
import Script from "next/script";
import { Analytics } from "@vercel/analytics/react";
// import GlobalHelpButton from "@/components/GlobalHelpButton"; // re‑enable if you need it

export const metadata = {
  title: "Detail On The Go",
  description:
    "Detail On The Go offers professional mobile car, boat, and RV detailing services at your location. Convenient, quality cleaning and protection for your vehicle.",
};

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html lang="en">
      <head>
        {/* Google Tag Manager */}
        <Script id="gtm-init" strategy="afterInteractive">
          {`
            (function(w,d,s,l,i){w[l]=w[l]||[];w[l].push({'gtm.start':
new Date().getTime(),event:'gtm.js'});var f=d.getElementsByTagName(s)[0],
j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';j.async=true;j.src=
'https://www.googletagmanager.com/gtm.js?id='+i+dl;f.parentNode.insertBefore(j,f);
})(window,document,'script','dataLayer','GTM-WJHQVBP5');
          `}
        </Script>
        {/* End Google Tag Manager */}
        <meta name="ahrefs-site-verification" content="840148bf2584b49e40ca822eeec96113da93c55846b30fb9409081c4c7937592" />
        {/* Open Graph meta tags */}
        <meta property="og:title" content="Detail On The Go - Professional Mobile Car, Boat and RV Detailing" />
        <meta property="og:type" content="website" />
        <meta property="og:image" content="https://www.detailongo.com/opengraph-image?3946d48eb898b74a" />
        <meta property="og:url" content="https://www.detailongo.com/" />
        <meta property="og:description" content="Detail On The Go offers professional mobile car, boat, and RV detailing services at your location. Enjoy convenient, high-quality cleaning and protection for your vehicles." />
        <meta property="og:image:type" content="image/png" />
        <meta property="og:image:alt" content="Detail On The Go - Professional Mobile Car, Boat and RV Detailing" />
      </head>
      <body
        className={cx(
          helvetica.variable,
          "min-h-screen animate-moving-gradient flex flex-col",
          "bg-gradient-to-br from-palatinate_blue-900 to-palatinate_blue-900",
          "text-gray-100"
        )}
      >
        {/* Google Tag Manager (noscript) */}
        <noscript>
          <iframe
            src="https://www.googletagmanager.com/ns.html?id=GTM-WJHQVBP5"
            height="0"
            width="0"
            style={{ display: "none", visibility: "hidden" }}
          ></iframe>
        </noscript>
        {/* End GTM noscript */}

        <Navbar />
        <main className="pt-20 flex-grow container mx-auto px-1 sm:px-1">
          {children}
        </main>
        <Footer />
        {/* <GlobalHelpButton /> */}
        <Analytics />
      </body>
    </html>
  );
}