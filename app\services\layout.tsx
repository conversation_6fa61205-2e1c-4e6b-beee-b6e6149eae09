import { Metadata } from 'next';

export const metadata: Metadata = {
    title: 'Auto Detailing Services | Detail On The Go',
    description: 'Professional car, boat, and RV detailing services. Mobile detailing at your location. Book online for interior, exterior, ceramic coating, and more.',
    keywords: [
        'auto detailing',
        'car detailing',
        'mobile detailing',
        'ceramic coating',
        'boat detailing',
        'RV detailing',
        'interior cleaning',
        'exterior wash',
        'Detail On The Go'
    ],
    alternates: {
        canonical: 'https://detailongo.com/services/',
    },
    openGraph: {
        title: 'Auto Detailing Services | Detail On The Go',
        description: 'Professional car, boat, and RV detailing services. Mobile detailing at your location.',
        url: 'https://detailongo.com/services/',
        images: [
            {
                url: '/images/services-og.jpg',
                width: 1200,
                height: 630,
                alt: 'Auto Detailing Services'
            }
        ],
        type: 'website',
    },
    twitter: {
        card: 'summary_large_image',
        title: 'Auto Detailing Services | Detail On The Go',
        description: 'Professional car, boat, and RV detailing services. Mobile detailing at your location.',
        images: ['/images/services-og.jpg'],
    }
};

// Add the required default export for the layout
export default function Layout({ children }: { children: React.ReactNode }) {
    return (
        <>
            {/* Structured Data for SEO */}
            <script
                type="application/ld+json"
                dangerouslySetInnerHTML={{
                    __html: JSON.stringify({
                        "@context": "https://schema.org",
                        "@type": "Service",
                        "name": "Auto Detailing Services",
                        "provider": [
                            {
                                "@type": "LocalBusiness",
                                "name": "Detail On The Go - Lawrence",
                                "url": "https://detailongo.com/",
                                "image": "https://detailongo.com/images/services-og.jpg",
                                "telephone": "******-615-6156",
                                "address": {
                                    "@type": "PostalAddress",
                                    "streetAddress": "935 Iowa St",
                                    "addressLocality": "Lawrence",
                                    "addressRegion": "KS",
                                    "postalCode": "66044",
                                    "addressCountry": "USA"
                                }
                            },
                            {
                                "@type": "LocalBusiness",
                                "name": "Detail On The Go - Kansas City",
                                "url": "https://detailongo.com/",
                                "image": "https://detailongo.com/images/services-og.jpg",
                                "telephone": "******-555-1234",
                                "address": {
                                    "@type": "PostalAddress",
                                    "streetAddress": "123 Main St",
                                    "addressLocality": "Kansas City",
                                    "addressRegion": "MO",
                                    "postalCode": "64101",
                                    "addressCountry": "USA"
                                }
                            },
                            {
                                "@type": "LocalBusiness",
                                "name": "Detail On The Go - Topeka",
                                "url": "https://detailongo.com/",
                                "image": "https://detailongo.com/images/services-og.jpg",
                                "telephone": "******-555-5678",
                                "address": {
                                    "@type": "PostalAddress",
                                    "streetAddress": "456 Elm St",
                                    "addressLocality": "Topeka",
                                    "addressRegion": "KS",
                                    "postalCode": "66601",
                                    "addressCountry": "USA"
                                }
                            }
                            // ...add more franchise locations as needed...
                        ],
                        "areaServed": [
                            "Lawrence, KS",
                            "Kansas City, MO",
                            "Topeka, KS"
                            // ...add more areas as needed...
                        ],
                        "serviceType": [
                            "Car Detailing",
                            "Boat Detailing",
                            "RV Detailing",
                            "Ceramic Coating"
                        ],
                        "url": "https://detailongo.com/services/"
                    })
                }}
            />
            {children}
        </>
    );
}